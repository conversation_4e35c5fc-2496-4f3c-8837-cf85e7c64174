<legend class="form-section-header">Patient Statements</legend>
<div class="row">
    <div class="col-sm-4 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.use_return_address.$invalid, 'has-success' : submitted && systemSettingsForm.use_return_address.$valid}">
        <label class="checkbox-inline checkbox-custom pt-25">
            <input type="checkbox" name="use_return_address" id="use_return_address" ng-true-value="'Y'"
                   ng-false-value="'N'"
                   ng-disabled="!editing"
                   ng-model="returnAddress.use_return_address.value"><i></i>
            Use Return Address for Patient Invoice?
        </label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.use_return_address.id"
                      showlabel="{{false}}"></audit-button>
    </div>
    <div class="col-sm-4 form-group">
        <label class="checkbox-inline checkbox-custom pt-25">
            <input type="checkbox" name="exclude_comment_export" id="exclude_comment_export"
                   ng-disabled="!editing" ng-model="billing.exclude_export_comments.value"
                   ng-true-value="'Y'"
                   ng-false-value="'N'"><i></i>
            Exclude Comments for Print and Export Statements.
        </label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.exclude_export_comments.id"
                      showlabel="{{false}}"></audit-button>
    </div>
    <div class="col-sm-4 form-group">
        <label class="checkbox-inline checkbox-custom pt-25">
            <input type="checkbox" name="exclude_notes_export" id="exclude_notes_export"
                   ng-disabled="!editing" ng-model="billing.exclude_export_notes.value"
                   ng-true-value="'Y'"
                   ng-false-value="'N'"><i></i>
            Exclude Notes for Export Statements.
        </label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.exclude_export_notes.id"
                      showlabel="{{false}}"></audit-button>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 form-group">
        <label class="checkbox-inline checkbox-custom pt-25">
            <input type="checkbox" name="exclude_patient_statement_l_codes" id="exclude_patient_statement_l_codes"
                   ng-disabled="!editing" ng-model="billing.exclude_patient_statement_l_codes.value"
                   ng-true-value="'Y'"
                   ng-false-value="'N'"><i></i>
            Exclude Patient Statement HCPCS Codes for Print & Export Statements.
        </label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.exclude_patient_statement_l_codes.id"
                      showlabel="{{false}}"></audit-button>
    </div>
    <div class="col-sm-6 form-group">
        <label class="checkbox-inline checkbox-custom pt-25">
            <input type="checkbox" name="include_patient_statement_sales_tax" id="include_patient_statement_sales_tax"
                   ng-disabled="!editing" ng-model="billing.include_patient_statement_sales_tax.value"
                   ng-true-value="'Y'"
                   ng-false-value="'N'"><i></i>
            Include Sales Tax Line Items for Print & Export Statements.
        </label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.include_patient_statement_sales_tax.id"
                      showlabel="{{false}}"></audit-button>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_sender_name.$invalid, 'has-success' : submitted && systemSettingsForm.return_sender_name.$valid}">
        <label for="return_sender_name">Sender Name</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_sender_name.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text" name="return_sender_name" id="return_sender_name" class="form-control input-sm"
               ng-required="returnAddress.use_return_address === 'Y'" ng-model="returnAddress.return_sender_name.value"
               ng-disabled="!editing">
        <div ng-messages="systemSettingsForm.return_sender_name.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="required">Sender name is required.</div>
        </div>
    </div>
    <div class="col-sm-6 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_street_address.$invalid, 'has-success' : submitted && systemSettingsForm.return_street_address.$valid}">
        <label for="return_street_address">Street</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_street_address.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text" name="return_street_address" id="return_street_address" class="form-control input-sm"
               ng-required="returnAddress.use_return_address === 'Y'"
               ng-model="returnAddress.return_street_address.value"
               ng-disabled="!editing">
        <div ng-messages="systemSettingsForm.return_street_address.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="required">Street is required.</div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-2 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_city.$invalid, 'has-success' : submitted && systemSettingsForm.return_city.$valid}">
        <label for="return_city">City</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_city.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text" name="return_city" id="return_city" class="form-control input-sm"
               ng-required="returnAddress.use_return_address === 'Y'" ng-model="returnAddress.return_city.value"
               ng-disabled="!editing">
        <div ng-messages="systemSettingsForm.return_city.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="required">City is required.</div>
        </div>
    </div>
    <div class="col-sm-2 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_state.$invalid, 'has-success' : submitted && systemSettingsForm.return_state.$valid}">
        <label for="return_state">State</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_state.id"
                      showlabel="{{false}}"></audit-button>
        <select name="return_state" id="return_state" chosen="{width: '240px'}"
                class="form-control input-sm chosen-select" ng-required="returnAddress.use_return_address === 'Y'"
                ng-disabled="!editing" ng-model="returnAddress.return_state.value">
            <option value=""></option>
            <option ng-repeat="(key, data) in states" value="{{key}}" ng-selected="key === returnAddress.return_state">
                {{data}}
            </option>
        </select>
        <div ng-messages="systemSettingsForm.return_state.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="required">State is required.</div>
        </div>
    </div>
    <div class="col-sm-2 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_zipcode.$invalid, 'has-success' : submitted && systemSettingsForm.return_zipcode.$valid}">
        <label for="return_zipcode">Zipcode</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_zipcode.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text" name="return_zipcode" id="return_zipcode" class="form-control input-sm bfh-phone zipcodes"
               ng-required="returnAddress.use_return_address === 'Y'" ng-model="returnAddress.return_zipcode.value"
               data-format="ddddd-dddd" ng-pattern="/^(\d{5}(-\d{4})?)$/" ng-disabled="!editing">
        <div ng-messages="systemSettingsForm.return_zipcode.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="required">Zipcode is required.</div>
            <div class="help-block" ng-message="pattern">Invalid format.</div>
        </div>
    </div>
    <div class="col-sm-4 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_po_box.$invalid, 'has-success' : submitted && systemSettingsForm.return_po_box.$valid}">
        <label for="return_po_box">PO Box</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_po_box.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text" name="return_po_box" id="return_po_box" class="form-control input-sm"
               ng-model="returnAddress.return_po_box.value" ng-disabled="!editing">
        <div ng-messages="systemSettingsForm.return_zipcode.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="required">Zipcode is required.</div>
            <div class="help-block" ng-message="pattern">Invalid format.</div>
        </div>
    </div>
    <div class="col-sm-2 form-group"
         ng-class="{ 'has-error' : submitted && systemSettingsForm.return_phone_number.$invalid, 'has-success' : submitted && systemSettingsForm.return_phone_number.$valid}">
        <label for="return_phone_number">Phone Number</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="returnAddress.return_phone_number.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text"
               name="return_phone_number"
               id="return_phone_number"
               class="form-control input-sm"
               ui-mask-placeholder
               ui-mask-placeholder-char="_"
               ui-mask="(************* Ext. ?9?9?9?9?9"
               ui-options="{clearOnBlur: false}"
               ng-cloak
               ng-model="returnAddress.return_phone_number.value" ng-disabled="!editing">
        <div ng-messages="systemSettingsForm.return_phone_number.$error" role="alert" ng-show="submitted">
            <div class="help-block" ng-message="pattern">Invalid format.</div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-3 form-group">
        <label for="pay_online_url">Pay Online at:</label>
        <audit-button ng-show="showSystemSettingsAudits" type="icon" entity="system_setting" ng-model="billing.pay_online_url.id"
                      showlabel="{{false}}"></audit-button>
        <input type="text" name="pay_online-url" id="pay_online_url" class="form-control input-sm"
               ng-model="billing.pay_online_url.value" ng-disabled="!editing">
    </div>
</div>

