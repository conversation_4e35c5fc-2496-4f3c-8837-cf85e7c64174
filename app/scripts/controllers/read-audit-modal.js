'use strict';
app.controller('ReadAuditModalCtrl', ReadAuditModalCtrl);
ReadAuditModalCtrl.$inject = ['$scope', '$filter', '$uibModalInstance', '$http', 'entity', 'id', 'parentColumn', 'description', 'partialEntity', 'UserService', 'UtilService'];

function ReadAuditModalCtrl($scope, $filter, $uibModalInstance, $http, entity, id, parentColumn, description, partialEntity, UserService, UtilService) {
	$scope.utilService = UtilService;
	$scope.isAdmin = UserService.isManagement() || UserService.isAdmin();

	var title = UtilService.fixCase(entity).replaceAll('L Code', 'HCPCS');
	$scope.title = title + ' Audits for #' + id;
	$scope.description = description;

	$scope.loading = true;
	$scope.audits = [];
	var url = 'api/' + entity.replaceAll('_', '-') + '/audit?id=' + id;
	if(parentColumn && parentColumn.length > 0){
		url = url +  '&columnName='+ parentColumn
	}

	$scope.viewLocation = function (ipAddress) {
		var locationUrl = 'api/geolocation/map/' + ipAddress;
		$http.get(locationUrl).success(function (response) {

			if (response.url)
			{
				window.open("https://" + response.url, '_blank');
			}
			else
			{
				// show message
				UtilService.displayAlert("danger", "Unable to determine location for IP Address, " + ipAddress, "#header-alert-container");
				$uibModalInstance.close();
			}
		})
	}

	$http.get(url).success(function (response) {
		angular.forEach(response, function (entry, index) {
			entry.$revisionDate = new Date(entry.revisionDate);
			try {
				var p = JSON.parse(entry.previousValue);
				if (p) {
					entry.previousValue = $scope.fixJson(p);
				}
			} catch (e) {
			}
			try {
				var c = JSON.parse(entry.currentValue);
				if (c) {
					entry.currentValue = $scope.fixJson(c);
				}
			} catch (e) {
			}
			if (partialEntity) {
				filterForEntityPartial(entry);
			} else {
				$scope.audits.push(entry);
			}
		});
		$scope.loading = false;
	});

	$scope.fixJson = function (json) {
		var r = '';
		Object.keys(json).forEach(function (key) {
			var t = key.replaceAll(/([A-Z])/g, " $1");
			t = t.charAt(0).toUpperCase() + t.slice(1);
			r = r + t + ' : ' + json[key] + '<br>';
		});
		if (r === '') r = json;
		return r;
	}

	$scope.done = function () {
		$uibModalInstance.close();
	}

	var filterForEntityPartial = function (entry) {
		// Function for future filtering we will need for other audits.
		if (partialEntity === 'proof_of_delivery') {
			if (entry.field === 'signedBy' || entry.field ===  'additionalComponentNotes' || entry.field ===  'deliveryLocation' || entry.field === 'deliveryLocationAddress') {
				$scope.audits.push(entry);
			}
		}
	}
}
