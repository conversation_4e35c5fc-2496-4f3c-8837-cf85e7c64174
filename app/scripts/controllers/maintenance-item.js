'use strict';
app.controller('ItemCtrl', ItemCtrl);
ItemCtrl.$inject = ['$rootScope', '$scope', '$uibModal', '$uibModalInstance', 'BranchFactory', 'BranchService', 'DiffModalService',
	'isModal', 'ItemService', 'LCodeCategoryFactory', 'LCodeFactory', 'oneTimePurchase', 'prescriptionPurchaseOrdersDto',
	'PurchasingService', 'SystemSettingFactory', 'UserFactory', 'UserService', 'UtilService', 'VendorFactory'];

function ItemCtrl($rootScope, $scope, $uibModal, $uibModalInstance, BranchFactory, BranchService, DiffModalService,
                  isModal, ItemService, LCodeCategoryFactory, LCodeFactory, oneTimePurchase, prescriptionPurchaseOrdersDto,
                  PurchasingService, SystemSettingFactory, UserFactory, UserService, UtilService, VendorFactory) {

	$scope.isModal = isModal;
	if (!$scope.isModal) {
		$rootScope.page = {
			title: 'Maintenance',
			subtitle: 'Items',
			view: 'item'
		};
	}
	$scope.branches = BranchFactory.query();
	$scope.branchService = BranchService;
	$scope.depreciationTypeChanging = false;
	$scope.depreciationUnitChanging = false;
	$scope.form = {};
	$scope.hasPermission = UserService.hasPermission;
	$scope.itemByManufacturerValid = false;
	$scope.itemService = ItemService;
	$scope.loading = false;
	$scope.nextCustomPoItem = null;
	$scope.prescriptionAttrs = null;
	$scope.prescriptionPurchaseOrdersDto = prescriptionPurchaseOrdersDto;
	$scope.purchasingService = PurchasingService;
	$scope.userService = UserService;
	$scope.users = UserFactory.search({active: true, companyId: UserService.getCompanyId()});

	$scope.filter = {
		categories: [],
		LCodeCategoryId: null,
		loading: false,
		numberOfElements: 0,
		oneTimePurchase: false,
		page: 0,
		searchTerm: "",
		showInactive: false,
		size: 1000,
		totalElements: 0,
		totalPages: 0
	};

	$scope.LCodeCategoryOptions = LCodeCategoryFactory.getCategoryOptions().$promise.then(function (response) {
		$scope.LCodeCategoryOptions = response;
	});

	$scope.mainTypes = LCodeCategoryFactory.getByClassification({classification: "main_type"});
	$scope.vendors = VendorFactory.search();
	$scope.itemsPhysicalFilter = {
		itemId: null,
		allStatuses: true,
		blankSerial: true
	};
	$scope.itemsPhysical = [];
	$scope.codedReasons = $('#codedReasons').attr('value').split(',');
	$scope.itemByManufacturerName = '';

	// Local functions
	function init(first) {
		if ($scope.prescriptionPurchaseOrdersDto) {
			$scope.prescriptionAttrs = {
				branchId: $scope.prescriptionPurchaseOrdersDto.branchId,
				deviceTypeName: $scope.prescriptionPurchaseOrdersDto.deviceTypeName,
				leftOrRight: $scope.prescriptionPurchaseOrdersDto.deviceTypeDTO.deviceType.leftOrRight,
				orthoticOrProsthetic: $scope.prescriptionPurchaseOrdersDto.deviceTypeDTO.deviceType.orthoticOrProsthetic,
				patientId: $scope.prescriptionPurchaseOrdersDto.patientId,
				prescriptionId: $scope.prescriptionPurchaseOrdersDto.prescriptionId,
				upperOrLower: $scope.prescriptionPurchaseOrdersDto.deviceTypeDTO.deviceType.upperOrLower
			};
			// Customers want Part Number of the Item to be unique, made of prescriptionId followed by a dash and ordinal number, e.g.: 12345-1, 12345-2, etc.
			$scope.nextCustomPoItem = null;
			// collect partNumbers that start with the prescriptionId from all Purchase Orders
			if ($scope.prescriptionPurchaseOrdersDto.purchaseOrderDTOs != null && $scope.prescriptionPurchaseOrdersDto.purchaseOrderDTOs.length > 0) {
				var customPoItems = [];
				for (const po of $scope.prescriptionPurchaseOrdersDto.purchaseOrderDTOs) {
					if (po.purchaseOrderItems && po.purchaseOrderItems.length > 0) {
						for (const poi of po.purchaseOrderItems) {
							if (poi.item && poi.item.itemByManufacturer && poi.item.itemByManufacturer.partNumber &&
								poi.item.itemByManufacturer.partNumber.startsWith($scope.prescriptionPurchaseOrdersDto.prescriptionId.toString())) {
								customPoItems.push(poi.item.itemByManufacturer.partNumber);
							}
						}
					}
				}
				// calculate the current greatest value of: suffix of all existing partNumber suffixes or total number of those partNumbers
				if (customPoItems.length > 0) {
					var pnIndex = 0;
					for (const partNumber of customPoItems) {
						if (partNumber) {
							if (partNumber.lastIndexOf('-') > 0 && partNumber.lastIndexOf('-') < partNumber.length - 1) {
								var pnSuffix = partNumber.substring(partNumber.lastIndexOf('-') + 1);
								var numSuffix = parseInt(pnSuffix);
								if (!isNaN(numSuffix) && numSuffix > pnIndex) {
									pnIndex = numSuffix;
								}
							} else if (partNumber == $scope.prescriptionPurchaseOrdersDto.prescriptionId.toString() && pnIndex == 0) {
								pnIndex = 1;
							}
						}
					}
					// increment the current suffix value by 1
					if (pnIndex >= customPoItems.length) {
						pnIndex++;
					} else {
						pnIndex = customPoItems.length + 1;
					}
					// assign $scope.nextCustomPoItem
					$scope.nextCustomPoItem = $scope.prescriptionPurchaseOrdersDto.prescriptionId.toString() + '-' + pnIndex;
				}
			}
			if (!$scope.nextCustomPoItem) {
				$scope.nextCustomPoItem = $scope.prescriptionPurchaseOrdersDto.prescriptionId.toString() + '-1';
			}
		}
		$scope.activeItemByManufacturer = activeContainer(null);
		$scope.activeRecord = null;
		$scope.backupItem = null;
		$scope.backupItemByManufacturer = null;
		$scope.editing = false;
		$scope.editingItemByManufacturer = false;
		$scope.savedItemByManufacturer = false;
		$scope.submitted = false;
		$scope.submittedItemByManufacturer = false;
		if (!first && $scope.form && $scope.form.item) {
			$scope.form.item.$setPristine();
			$scope.form.item.$setUntouched();
		}
		if ($scope.isModal) {
			$scope.newRecord();
		}
	}
	function activeContainer(itemByManufacturer) {
		var valOut = {
			itemByManufacturer: null,
			itemByManufacturerId: null,
			manufacturerId: null
		};
		if (itemByManufacturer) {
			valOut.itemByManufacturer = angular.copy(itemByManufacturer);
			valOut.itemByManufacturerId = itemByManufacturer.id;
			valOut.manufacturerId = itemByManufacturer.manufacturerId;
		}
		return valOut;
	}
	function blankItemByManufacturer() {
		return {
			id: null,
			active: true,
			createdAt: null,
			createdBy: null,
			createdById: null,
			description: null,
			keywords: null,
			lCodeCategoryId: null,
			lCodes: [],
			manufacturerId: null,
			msrp: null,
			name: null,
			partNumber: null,
			upc: null,
			upcType: null
		};
	}
	function clearItemByManufacturer(clearParent) {
		$scope.editingItemByManufacturer = false;
		$scope.submittedItemByManufacturer = false;

		if (clearParent && $scope.activeRecord) {
			$scope.activeRecord.itemByManufacturer = null;
			$scope.activeRecord.itemByManufacturerId = null;
			$scope.activeRecord.manufacturerId = null;
		}
		$scope.activeItemByManufacturer = activeContainer(null);
	}

	// $scope functions
	$scope.addItemByManufacturer = function () {
		if (!$scope.editing) {
			$scope.activeRecord = null;
		}
		clearItemByManufacturer(false);
		$scope.editingItemByManufacturer = true;
		$scope.itemByManufacturerValid = true;
		if (!$scope.backupItemByManufacturer && $scope.activeItemByManufacturer.itemByManufacturer) {
			$scope.backupItemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
		}
		$scope.activeItemByManufacturer.itemByManufacturer = blankItemByManufacturer();
		$scope.activeItemByManufacturer.itemByManufacturerId = null;
		if ($scope.isModal && $scope.prescriptionAttrs) {
			switch ($scope.prescriptionAttrs.orthoticOrProsthetic) {
				case 'orthotic':
					$scope.activeItemByManufacturer.itemByManufacturer.LCodeCategoryId = 2;
					break;
				case 'prosthetic':
					$scope.activeItemByManufacturer.itemByManufacturer.LCodeCategoryId = 1;
					break;
				default:
					$scope.activeItemByManufacturer.itemByManufacturer.LCodeCategoryId = null;
					break;
			}
			$scope.activeItemByManufacturer.itemByManufacturer.name = $scope.prescriptionAttrs.deviceTypeName
			$scope.activeItemByManufacturer.itemByManufacturer.partNumber = $scope.nextCustomPoItem;
			/* TODO: we may add this later
			if ($scope.prescriptionAttrs.upperOrLower
			 && !$scope.activeItemByManufacturer.itemByManufacturer.name.toUpper().includes($scope.prescriptionAttrs.upperOrLower.toUpper())) {
				$scope.activeItemByManufacturer.itemByManufacturer.name += ' ' + $scope.prescriptionAttrs.upperOrLower;
			}
			if ($scope.prescriptionAttrs.leftOrRight
			 && !$scope.activeItemByManufacturer.itemByManufacturer.name.toUpper().includes($scope.prescriptionAttrs.leftOrRight.toUpper())) {
				$scope.activeItemByManufacturer.itemByManufacturer.name += ' ' + $scope.prescriptionAttrs.leftOrRight;
			} */
		}
	};

	$scope.adjustInventory = function () {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/_inventory_items_modal.html',
			controller: 'InventoryItemsModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				showMatches: function () {
					return false;
				},
				items: function () {
					return null;
				},
				item: function () {
					return $scope.activeRecord;
				}
			}
		}).closed.then(function () {
			$scope.itemsPhysical = [];
			$scope.setActive($scope.activeRecord);
		});
	};

	$scope.addLCode = function ($item, $model, $label) {
		$scope.activeItemByManufacturer.itemByManufacturer.lCodes.push($item);
	};

	$scope.cancel = function () {
		if (!$scope.savedItemByManufacturer) {
			$scope.cancelItemByManufacturer();
		}
		$scope.editing = false;
		$scope.submitted = false;
		if ($scope.backupItem) {
			$scope.activeRecord = angular.copy($scope.backupItem);
			$scope.backupItem = null;
			// if underlying itemByManufacturer was modified, we still need to update the collection of Items
			if ($scope.savedItemByManufacturer) {
				$scope.savedItemByManufacturer = false;
				$scope.activeRecord.itemByManufacturer = $scope.activeItemByManufacturer.itemByManufacturer;
				$scope.activeRecord.itemByManufacturerId = $scope.activeItemByManufacturer.itemByManufacturerId;
				$scope.activeRecord.manufacturerId = $scope.activeItemByManufacturer.manufacturerId;
				$scope.activeRecord.name = $scope.activeItemByManufacturer.itemByManufacturer.name;
				for (var i = 0; i < $scope.items.length; i++) {
					if ($scope.items[i].id == $scope.activeRecord.id) {
						$scope.items[i] = angular.copy($scope.activeRecord);
						break;
					}
				}
			}
		} else {
			$scope.activeRecord = null;
		}
		if ($scope.isModal) {
			$uibModalInstance.close(null);
		}
	};

	$scope.cancelItemByManufacturer = function () {
		$scope.editingItemByManufacturer = false;
		$scope.submittedItemByManufacturer = false;
		if ($scope.backupItemByManufacturer && $scope.backupItemByManufacturer.id > 0) {
			$scope.activeItemByManufacturer = activeContainer($scope.backupItemByManufacturer);
			$scope.backupItemByManufacturer = null;
			if ($scope.activeRecord) {
				$scope.activeRecord.itemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
				$scope.activeRecord.itemByManufacturerId = $scope.activeItemByManufacturer.itemByManufacturerId;
				$scope.activeRecord.manufacturerId = $scope.activeItemByManufacturer.manufacturerId;
			}
		} else {
			$scope.activeItemByManufacturer = activeContainer(null);
		}
	};

	$scope.delete = function () {
		if (confirm('Are you sure you want to delete the current item?')) {
			$scope.editing = false;
			var itemId = $scope.activeRecord.id;
			var itemName = $scope.activeRecord.name;
			ItemService.deleteItem(itemId).then(function (response) {
				if (response == 'OK') {
					// remove the deleted Item from the collection
					var index = 0;
					for (index = 0; index < $scope.items.length; index++) {
						if ($scope.items[index].id == itemId) {
							$scope.items.splice(index, 1);
							break;
						}
					}
					$scope.activeRecord = null;
					if ($scope.form && $scope.form.item) {
						$scope.form.item.$setPristine();
					}
					if ($scope.items.length > 1) {
						if (index > 0) index--;
						$scope.setActive($scope.items[index]);
					} else {
						$scope.search($scope.filter.page);
					}
					alert("The item " + itemName + " was successfully deleted.");
				} else {
					alert(response);
				}
			}, function (error) {
				alert(error);
			});
		}
	};

	$scope.deleteLCode = function (lCodeId) {
		var index = $scope.activeItemByManufacturer.itemByManufacturer.lCodes.findIndex(function (lc) { return lc.id == lCodeId; });
		if (index >= 0) {
			$scope.activeItemByManufacturer.itemByManufacturer.lCodes.splice(index, 1);
		}
	};

	/**
	 * This function doesn't delete a physical item but rather just sets its status to 'discarded'
	 */
	$scope.deleteItemPhysical = function (itemPhysicalId) {
		var itemPhysical = getItemPhysical(itemPhysicalId);
		var confirmText = "Are you sure you want to delete item '"
			+ itemPhysical.item.name + (itemPhysical.serialNumber ? ("' (SN: " + itemPhysical.serialNumber + ")") : "'");
		if (itemPhysical && confirm(confirmText)) {
			if (!itemPhysical.reason || $scope.codedReasons.includes(itemPhysical.reason)) itemPhysical.reason = 'item_maintenance';
			itemPhysical.status = 'discarded';
			ItemService.saveItemPhysical(itemPhysical).then(function (response) {
				if (response) {
					angular.forEach($scope.itemsPhysical, function (ii, index) {
						if (ii.id == itemPhysical.id) {
							$scope.itemsPhysical.splice(index, 1);
						}
					});
				}
			});
		}
	};

	$scope.depreciationTypeChanged = function () {
		$scope.depreciationTypeChanging = true;
		var depreciationUnit = $scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit;
		if (!$scope.depreciationUnitChanging) {
			switch ($scope.activeItemByManufacturer.itemByManufacturer.depreciationType) {
				case 'ongoing_upon_receipt':
					$scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit = 'month';
					break;
				case 'sale_distributed':
					$scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit = 'rental_cycle';
					break;
				default:
					$scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit = null;
					break;
			}
		}
		if ((depreciationUnit == null && $scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit != null) ||
			(depreciationUnit != null && $scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit == null) ||
			(depreciationUnit != null && depreciationUnit != $scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit)) {
			$scope.depreciationUnitChanged();
		}
		$scope.depreciationTypeChanging = false;
	};

	$scope.depreciationUnitChanged = function () {
		$scope.depreciationUnitChanging = true;
		if (!$scope.depreciationTypeChanging) {
			switch ($scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit) {
				case 'month':
					$scope.activeItemByManufacturer.itemByManufacturer.depreciationType = 'ongoing_upon_receipt';
					break;
				case 'rental_cycle':
					$scope.activeItemByManufacturer.itemByManufacturer.depreciationType = 'sale_distributed';
					break;
				default:
					$scope.activeItemByManufacturer.itemByManufacturer.depreciationType = null;
					break;
			}
		}
		$scope.activeItemByManufacturer.itemByManufacturer.depreciationUnitsToZero = ($scope.activeItemByManufacturer.itemByManufacturer.depreciationUnit == 'month') ? 60 : 13;
		$scope.depreciationUnitChanging = false;
	};

	$scope.edit = function () {
		$scope.editing = true;
		$scope.backupItem = angular.copy($scope.activeRecord);
		$scope.itemByManufacturerValid = $scope.activeItemByManufacturer.itemByManufacturer || $scope.editingItemByManufacturer;
		if ($scope.activeItemByManufacturer.itemByManufacturer) {
			$scope.itemByManufacturerName = $scope.activeItemByManufacturer.itemByManufacturer.name;
			if (!$scope.backupItemByManufacturer) {
				$scope.backupItemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
			}
		}
	};

	$scope.editItemByManufacturer = function () {
		$scope.editingItemByManufacturer = true;
		$scope.itemByManufacturerValid = true;
		if (!$scope.backupItemByManufacturer && $scope.activeItemByManufacturer.itemByManufacturer) {
			$scope.backupItemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
		}
	};

	$scope.editItemPhysical = function(itemPhysicalId) {
		var itemPhysical = getItemPhysical(itemPhysicalId);
		if (itemPhysical) {
			ItemService.openItemPhysicalModal('item', null, itemPhysical);
		} else {
			confirm("There is no Physical Item selected");
		}
	};

	$scope.filterCategories = function (position, type) {
		LCodeCategoryFactory.getByParentId({parentId: type.id}).$promise.then(function (response) {
			switch (type.classification) {
				case "main_type":
					angular.forEach($scope.mainTypes, function (mainType, index) {
						if (position !== index)
							mainType.$checked = false;
					});
					$scope.categories = [];
					$scope.subCategories = [];
					if ($scope.mainTypes[position].$checked) {
						$scope.subTypes = response;
						$scope.filter.lCodeCategoryId = type.id;
					} else {
						$scope.subTypes = [];
						$scope.filter.lCodeCategoryId = null;
					}
					break;
				case "sub_type":
					angular.forEach($scope.subTypes, function (subType, index) {
						if (position !== index)
							subType.$checked = false;
					});
					$scope.subCategories = [];
					if ($scope.subTypes[position].$checked) {
						$scope.categories = response;
						$scope.filter.lCodeCategoryId = type.id;
					} else {
						$scope.categories = [];
						$scope.filter.lCodeCategoryId = type.parentId;
					}
					break;
				case "category":
					angular.forEach($scope.categories, function (category, index) {
						if (position !== index)
							category.$checked = false;
					});
					if ($scope.categories[position].$checked) {
						$scope.subCategories = response;
						$scope.filter.lCodeCategoryId = type.id;
					} else {
						$scope.subCategories = [];
						$scope.filter.lCodeCategoryId = type.parentId;
					}
					break;
				default:
					angular.forEach($scope.subCategories, function (subCategory, index) {
						if (position !== index)
							subCategory.$checked = false;
					});
					if ($scope.subCategories[position].$checked) {
						$scope.filter.lCodeCategoryId = type.id;
					} else {
						$scope.filter.lCodeCategoryId = type.parentId;
					}
			}
			$scope.search();

		});
	};

	/**
	 * Format an arbitrary number "like" money, i.e. $1,499.95 or 33.33%
	 * @param origFloat - number to format
	 * @param prefix - usually '$'
	 * @param suffix - can be anything, like '%', 'mm', etc.
	 * @returns {string|null}
	 */
	$scope.formatDecimal = function(origFloat, prefix, suffix) {
		if (!prefix) prefix = '';
		if (!suffix) suffix = '';
		if (origFloat <= 0) {
			return prefix + '0.00' + suffix;
		}
		if (!origFloat || isNaN(origFloat)) {
			return null;
		}
		var strNum = roundMoney(origFloat).toString();
		var dotIndex = strNum.indexOf('.');
		// I want the "body" of the number to have commas
		var strRoot = dotIndex >= 0 ? strNum.substring(0, dotIndex) : strNum;
		if (strRoot.length > 3) {
			strRoot =	strRoot.split('').reverse().join('').replaceAll(/([0-9]{3})/g, "$1,");
			if (strRoot.lastIndexOf(',') == (strRoot.length - 1)) strRoot = strRoot.substring(0, strRoot.length - 1);
			strRoot = strRoot.split('').reverse().join('');
		}
		// now, format the decimal "tail"
		var tailLength = dotIndex >= 0 ? strNum.length - dotIndex : 0;
		var strTail = '.00';
		if (tailLength >= 2) {
			strTail = strNum.substring(dotIndex, dotIndex + 3);
		} else if (tailLength == 1) {
			strTail = strNum.substring(dotIndex) + '0';
		}
		// concatenate the two parts
		return prefix + strRoot + strTail + suffix;
	};

	$scope.getItemsPhysical = function() {
		if ($scope.activeRecord) {
			$scope.itemsPhysicalFilter.itemId = Number($scope.activeRecord.id);
			ItemService.getItemsPhysical($scope.itemsPhysicalFilter.itemId, $scope.itemsPhysicalFilter.allStatuses, $scope.itemsPhysicalFilter.blankSerial)
				.then(function (response) {
					$scope.itemsPhysical = response;
				});
		}
	};

	function getItemPhysical(itemPhysicalId) {
		var itemPhysical = null;
		if (itemPhysicalId && itemPhysicalId > 0 && $scope.itemsPhysical && $scope.itemsPhysical.length > 0) {
			itemPhysical = $scope.itemsPhysical.find(ip => ip.id == itemPhysicalId);
		}
		return itemPhysical;
	}

	$scope.importPriceCatalog = function () {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/maintenance/_import_price_catalog_modal.html',
			controller: 'ImportPriceCatalogModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				vendors: function () {
					return $scope.vendors;
				}
			}
		});

		modalInstance.result.then(function (result) {
			if (result.indexOf('Successfully') !== -1) {
				UtilService.displayAlert("success", "<p>" + result + "</p>", "#alert-container");
			} else if (result.indexOf('failed') !== -1) {
				UtilService.displayAlert("danger", "<p>" + result + "</p>", "#alert-container");
			}
			$scope.search();
		});
	};

	$scope.isUpcE = function () {
		return $scope.activeItemByManufacturer.itemByManufacturer && $scope.activeItemByManufacturer.itemByManufacturer.upcType == 'E';
	};

	$scope.itemByManufacturerBlur = function () {
		var newVal = $('#itemByManufacturer').val();
		if (newVal != $scope.itemByManufacturerName) {
			$scope.itemByManufacturerValid = $scope.editingItemByManufacturer;
			$scope.itemByManufacturerName = newVal;
		}
	}

	$scope.manufacturerChanged = function () {
		if (!$scope.backupItemByManufacturer && $scope.activeItemByManufacturer.itemByManufacturer) {
			$scope.backupItemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
		}
		angular.forEach($scope.vendors, function (vendor, index) {
			if (vendor.id == $scope.activeItemByManufacturer.manufacturerId && $scope.activeItemByManufacturer.itemByManufacturer) {
				$scope.activeItemByManufacturer.itemByManufacturer.manufacturer = angular.copy(vendor);
			}
		});
		// if adding or editing ItemByManufacturer
		if ($scope.editingItemByManufacturer && $scope.activeItemByManufacturer.itemByManufacturer) {
			$scope.activeItemByManufacturer.itemByManufacturer.manufacturerId = $scope.activeItemByManufacturer.manufacturerId;
		}
		// otherwise, if editing Item, changing manufacturer invalidates itemByManufacturer
		else if ($scope.editing && $scope.activeRecord) {
			$scope.activeItemByManufacturer.itemByManufacturer = null;
			$scope.activeItemByManufacturer.itemByManufacturerId = null;
			$scope.activeRecord.itemByManufacturer = null;
			$scope.activeRecord.itemByManufacturerId = null;
			$scope.activeRecord.manufacturerId = $scope.activeItemByManufacturer.manufacturerId;
		}
	};

	$scope.newRecord = function () {
		clearItemByManufacturer(true);
		$scope.backupItem = null;
		$scope.activeRecord = {
			id: null,
			active: !oneTimePurchase,
			itemByManufacturer: null,
			itemByManufacturerId: null,
			lCodes: [],
			manufacturer: null,
			manufacturerId: null,
			oneTimePurchase: oneTimePurchase,
			patientSalePrice: null,
			price: null,
			sku: null,
			users: [],
			vendor: null,
			vendorId: null
		};
		$scope.editing = true;
		if ($scope.isModal) {
			if ($scope.prescriptionAttrs) {
				$scope.activeRecord.sku = $scope.nextCustomPoItem;
				$scope.addItemByManufacturer();
			}
		} else {
			if ($scope.form && $scope.form.item) $scope.form.item.$setPristine();
		}
	};

	$scope.refreshLCodes = function (input) {
		return LCodeFactory.search({q: input, c: null, active: true}).$promise.then(function (response) {
			return response;
		});
	};

	$scope.oneTimePurchaseChanged = function () {
		if ($scope.filter.oneTimePurchase) {
			$scope.filter.showInactive = true;
		} else {
			$scope.filter.showInactive = false;
		}
		$scope.search(0);
	};

	function roundMoney(moneyFloat) {
		return Math.round(moneyFloat * 100) / 100;
	}

	$scope.save = function (isValid) {
		if ($scope.editingItemByManufacturer) {
			UtilService.displayAlert('danger', 'Please save the Manufacturer Item first', '');
			$('#save-form').button('reset');
		} else if (!isValid || !$scope.itemByManufacturerValid) {
			UtilService.displayAlert('danger', 'Please correct the errors in red', '');
			$('#save-form').button('reset');
		} else {
			$('#save-form').button('loading');
			ItemService.saveItem($scope.activeRecord).then(function (saved) {
				if (saved) {
					if ($scope.activeRecord.id && $scope.activeRecord.id > 0) {
						// modified an existing Item
						for (var i = 0; i < $scope.items.length; i++) {
							if ($scope.items[i].id == saved.id) {
								$scope.items[i] = angular.copy(saved);
								break;
							}
						}
					} else {
						// added a new Item
						$scope.items.push(angular.copy(saved));
					}
					$scope.setActive(saved);

					// Conditionally create Custom Purchase Order
					if ($scope.prescriptionPurchaseOrdersDto) {
						$scope.purchasingService.createCustomPurchaseOrder($scope.prescriptionPurchaseOrdersDto, $scope.activeRecord);
					} else {
						// Close the modal or update the view
						if ($scope.isModal) {
							$uibModalInstance.close(saved);
						} else {
							if ($scope.form && $scope.form.item) {
								$scope.form.item.$setPristine();
							}
							$scope.editing = false;
							$scope.submitted = false;
						}
					}
				}
				$('#save-form').button('reset');
			});
		}
		$scope.submitted = true;
	};

	$scope.saveItemByManufacturer = function (isValid) {
		$scope.submittedItemByManufacturer = true;
		if (!isValid || !$scope.itemByManufacturerValid) {
			UtilService.displayAlert('danger', 'Please correct the errors in red', '');
			$('#save-form').button('reset');
			return false;
		} else {
			if (!$scope.activeItemByManufacturer.itemByManufacturer.upc) {
				$scope.activeItemByManufacturer.itemByManufacturer.upcType = null;
			}
			ItemService.saveItemByManufacturer($scope.activeItemByManufacturer.itemByManufacturer).then(function (response) {
				if (response) {
					$scope.activeItemByManufacturer = activeContainer(response);
					$scope.itemByManufacturerValid = true;
					$scope.backupItemByManufacturer = null;
					if ($scope.activeRecord) {
						$scope.activeRecord.itemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
						$scope.activeRecord.itemByManufacturerId = $scope.activeItemByManufacturer.itemByManufacturerId;
						$scope.activeRecord.manufacturerId = $scope.activeItemByManufacturer.manufacturerId;
						// refresh the item in the parent collection
						if (!$scope.editing) {
							for (var i = 0; i < $scope.items.length; i++) {
								if ($scope.items[i].id == $scope.activeRecord.id) {
									$scope.items[i] = angular.copy($scope.activeRecord);
									break;
								}
							}
						}
					}
					$scope.submittedItemByManufacturer = false;
					$scope.editingItemByManufacturer = false;
					$scope.savedItemByManufacturer = true;
					return true;
				} else {
					return false;
				}
			});
		}
	};

	$scope.search = function (page) {
		$scope.filter.loading = true;
		init(true);
		$scope.data = {items: []};
		if (page === 0) {
			$scope.filter.page = page;
		}
		$scope.filter.totalElements = 0;
		$scope.filter.totalPages = 0;
		var params = {
			columnName: null,
			keywords: $scope.filter.searchTerm,
			lcodeCategoryId: $scope.filter.lCodeCategoryId,
			oneTimePurchase: $scope.filter.oneTimePurchase,
			page: $scope.filter.page == 0 ? $scope.filter.page : $scope.filter.page - 1,
			showInactive: $scope.filter.showInactive,
			size: $scope.filter.size,
			sortDirection: null,
			vendorId: null
		};
		ItemService.getItems(params).then(function (response) {
			$scope.filter.loading = false;
			if (response && response.size > 0) {
				$scope.items = response.content;
				$scope.filter.page = response.number + 1;
				$scope.filter.size = response.size;
				$scope.filter.totalElements = response.totalElements;
				$scope.filter.totalPages = response.totalPages;
			}
		});
	};

	$scope.selectItem = function (item) {
		if ($scope.activeRecord) {
			$uibModalInstance.close(item);
		}
	};

	$scope.selectItemByManufacturer = function (ibm) {
		if (ibm) {
			$scope.itemByManufacturerValid = true;
			$scope.itemByManufacturerName = ibm.name;
			if (!$scope.backupItemByManufacturer && $scope.activeItemByManufacturer.itemByManufacturer) {
				$scope.backupItemByManufacturer = angular.copy($scope.activeItemByManufacturer.itemByManufacturer);
			}
			$scope.activeItemByManufacturer = activeContainer(ibm);
			if ($scope.activeRecord) {
				$scope.activeRecord.itemByManufacturer = angular.copy(ibm);
				$scope.activeRecord.itemByManufacturerId = ibm.id;
				$scope.activeRecord.lCodes = ibm.lCodes;
				$scope.activeRecord.manufacturerId = ibm.manufacturerId;
				if (!$scope.activeRecord.vendorId) {
					$scope.activeRecord.vendor = ibm.manufacturer;
					$scope.activeRecord.vendorId = ibm.manufacturerId;
				}
			}
		}
	};

	$scope.setActive = function (record) {
		if (record) {
			$scope.activeRecord = angular.copy(record);
			$scope.activeItemByManufacturer = activeContainer(record.itemByManufacturer);
			$scope.getItemsPhysical();
		} else {
			init(false);
			$scope.itemsPhysical = [];
		}
		$scope.editing = false;

		$scope.updateLabel(null);
	};

	$scope.showInactiveChanged = function () {
		if (!$scope.filter.showInactive) {
			$scope.filter.oneTimePurchase = false;
		}
		$scope.search(0);
	};

	$scope.updateLabel = function () {
		$scope.LCodeCategoryLabel = "";
		if ($scope.activeRecord && $scope.activeRecord.LCodeCategory) {
			var recursiveParent = $scope.activeRecord.LCodeCategory.parent;
			var label = $scope.activeRecord.LCodeCategory.category;
			while (recursiveParent) {
				label = recursiveParent.category + '->' + label;
				recursiveParent = recursiveParent.parent;
			}
			$scope.LCodeCategoryLabel = label;
		}
	};

	$scope.upnNumberChanged = function () {
		if ($scope.activeItemByManufacturer.itemByManufacturer.upn == '') {
			$scope.activeItemByManufacturer.itemByManufacturer.upn = null;
		}
		if (!$scope.activeItemByManufacturer.itemByManufacturer.upn) {
			$scope.activeItemByManufacturer.itemByManufacturer.upnQualifier = null;
		}
	};

	$scope.upnQualifierChanged = function () {
		if ($scope.activeItemByManufacturer.itemByManufacturer.upnQualifier == '') {
			$scope.activeItemByManufacturer.itemByManufacturer.upnQualifier = null;
		}
		if (!$scope.activeItemByManufacturer.itemByManufacturer.upnQualifier) {
			$scope.activeItemByManufacturer.itemByManufacturer.upn = null;
		}
	};

	$scope.vendorChanged = function() {
		if ($scope.activeRecord && $scope.activeRecord.vendorId) {
			var vendor = $scope.vendors.find(function (v) { return v.id == $scope.activeRecord.vendorId; });
			$scope.activeRecord.vendor = vendor ? angular.copy(vendor) : null;
		}
	};

	$scope.search();

	$rootScope.$on('itemPhysicalModalClosed', function(event, args) {
		$scope.itemsPhysical = [];
		$scope.setActive($scope.activeRecord);
	});
}

