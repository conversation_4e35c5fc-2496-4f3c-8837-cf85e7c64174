'use strict';
app.controller('PatientStatementCtrl', PatientStatementCtrl);
PatientStatementCtrl.$inject = ['$rootScope', '$scope', '$filter', '$moment', '$uibModal', 'orderByFilter', 'ClaimFactory', 'UserService', 'StatementService', 'PatientService', 'InsuranceService', 'UtilService', 'SystemSettingFactory', 'BranchService', 'PaymentService'];

function PatientStatementCtrl($rootScope, $scope, $filter, $moment, $uibModal, orderBy, ClaimFactory, UserService, StatementService, PatientService, InsuranceService, UtilService, SystemSettingFactory, BranchService, PaymentService) {
	$rootScope.page = {
		title: 'Billing',
		subtitle: 'Patient Statements'
	};

	$scope.$on('localStorageObjectLoadComplete', function () {
		$scope.statementService = StatementService;
		$scope.patientService = PatientService;
		$scope.branchService = BranchService;
		$scope.utilService = UtilService;
		$scope.insuranceService = InsuranceService.getInsurances();
		$scope.user = UserService.getCurrentUser();
		$scope.needsReview = 'N';
		$scope.excludeSetting = SystemSettingFactory.findBySectionAndField({
			section: "billing",
			field: "exclude_export_comments"
		});
		$scope.excludeNoteSetting = SystemSettingFactory.findBySectionAndField({
			section: "billing",
			field: "exclude_export_notes"
		});
		$scope.excludePatientStatementLCodes = SystemSettingFactory.findBySectionAndField({
			section: "billing",
			field: "exclude_patient_statement_l_codes"
		});
		$scope.include_patient_statement_sales_tax = SystemSettingFactory.findBySectionAndField({
			section: "billing",
			field: "include_patient_statement_sales_tax"
		});
		SystemSettingFactory.findBySectionAndField({
			section: "billing",
			field: "patient_statement_needs_review"
		}).$promise.then(function (response) {
			$scope.needsReview = response.value;
		});
		SystemSettingFactory.findBySectionAndField({
			section: "general",
			field: "use_note_subject"
		}).$promise.then(function (response) {
			$scope.useNoteSubject = response.value === 'Y';
		});
		$scope.moment = $moment;
		$scope.selected = [];
		$scope.propertyName = 'statement.ageInDays';
		$scope.reverse = true;
		$scope.matches = 0;
		$scope.loading = false;
		$scope.totalPages = 0;
		$scope.resetPagination = false;
		$scope.pageSize = 100;
		$scope.collectionsExportInProgress = false;
		$scope.wrapper = {
			selectedAll: false
		};

		$scope.selectedStatements = [];

		$scope.filter = {
			patientId: undefined,
			patient: undefined,
			branchId: undefined,
			insuranceCompanyId: null,
			columnName: 'cs.submission_date',
			sortDirection: 'ASC',
			lastSentStartDate: "",
			lastSentEndDate: "",
			dosStartDate: "",
			dosEndDate: "",
			attempt: "all",
			unappliedPayments: false,
			pageNumber: 1,
			pageSize: 100
		};

		$scope.pageSizes = [25, 50, 100, 200, 300, 400, 500];
	});
	$scope.selectPatient = function ($item) {
		if ($item) {
			$scope.clearFilters("patient");
			$scope.filter.patientId = $item.id;
			$scope.filter.patient = $item;
		}
	};

	$scope.deselectPatient = function () {
		if ($scope.filter.patientId) {
			$scope.filter.patient = undefined;
			$scope.filter.patientId = undefined;
		}
	};

	$scope.$watchGroup(["filter.patientId", "filter.lastSentStartDate", "filter.lastSentEndDate"], function (newValues, oldValues, scope) {
		$scope.resetPagination = true;
	});

	$scope.search = function () {
		if ($scope.filter.dosStartDate && !$scope.filter.dosEndDate || !$scope.filter.dosStartDate && $scope.filter.dosEndDate) {
			alert('Both date of service parameters must be selected');
			return;
		}
		if ($scope.filter.claimId != null) {
			if (isNaN($scope.filter.claimId) || $scope.filter.claimId % 1 != 0
				|| $scope.filter.claimId < 0 || $scope.filter.claimId > **********) {
				alert('ERROR: Claim ID must be a valid number');
				$scope.filter.claimId = null;
				return;
			}
		}
		if ($scope.filter.prescriptionId != null) {
			if (isNaN($scope.filter.prescriptionId) || $scope.filter.prescriptionId % 1 !== 0
				|| $scope.filter.prescriptionId < 0 || $scope.filter.prescriptionId > **********) {
				alert('ERROR: Rx ID must be a valid number');
				$scope.filter.prescriptionId = null;
				return;
			}
		}
		$scope.loading = true;
		$scope.filter.pageNumber = $scope.resetPagination ? 1 : $scope.filter.pageNumber;
		$scope.filter.pageSize = $scope.pageSize;
		if ($scope.filter.dosStartDate && $scope.filter.dosEndDate) {
			$scope.filter.dosStartDate = $moment($scope.filter.dosStartDate).format("YYYY-MM-DD");
			$scope.filter.dosEndDate = $moment($scope.filter.dosEndDate).format("YYYY-MM-DD");
		}
		ClaimFactory.patientStatements($scope.filter).$promise.then(function (response) {
			$scope.totalPages = response.totalPages;
			$scope.matches = response.totalElements;
			$scope.statements = response.content;
			$scope.loading = false;
			$scope.resetPagination = false;
			var amountSelected = 0;
			angular.forEach($scope.statements, function (statement) {
				if ($scope.selectedStatements.filter(function (e) {
					return e.claimId === statement.claimId;
				}).length > 0) {
					statement.selected = true;
					amountSelected++;
				}

				if (statement.latestPatientStatement && !statement.claim.paymentPlan) {
					switch (statement.latestPatientStatement.status) {
						case "attempt_1":
							statement.nextAttempt = "attempt_2";
							break;
						case "attempt_2":
							statement.nextAttempt = "attempt_3";
							break;

						case "attempt_3":
							statement.nextAttempt = $scope.needsReview === "Y" ? "needs_review" : "sent_to_collections";
							break;

						case "sent_to_collections":
							statement.nextAttempt = "sent_to_collections";
							break;
						case "needs_review":
							statement.nextAttempt = "needs_review";
							break;
						default:
							statement.nextAttempt = "attempt_1";
					}
				} else if (statement.latestPatientStatement) {
					statement.nextAttempt = statement.latestPatientStatement.status;
				} else {
					statement.nextAttempt = "attempt_1";
				}
				if (statement.lastPayer === 'insurance_company') {
					statement.lastPayer = 'Insurance';
				} else if (statement.lastPayer === 'patient') {
					statement.lastPayer = 'Patient';
				} else {
					return;
				}
			});
			$scope.wrapper.selectedAll = amountSelected === $scope.statements.length;
		});
	};

	$scope.clear = function () {
		$scope.clearFilters("all");
	};

	$scope.sortBy = function (columnName) {
		$scope.filter.columnName = columnName;
		$scope.filter.sortDirection = $scope.filter.sortDirection === 'ASC' ? 'DESC' : 'ASC';
		$scope.loading = false;
		$scope.statements = [];
		$scope.search();
	};

	$scope.exportStatements = function (exportType) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/billing/_export_patient_statements_modal_form.html',
			controller: 'ExportPatientStatementsModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				statements: function () {
					var sortedSelectedStatements = orderBy($scope.selectedStatements, 'ageInDays', true);
					return sortedSelectedStatements;
				},
				exportType: function () {
					return exportType;
				},
				needsReview: function () {
					return $scope.needsReview === 'Y';
				},
			}
		});
		modalInstance.result.then(function () {
			$scope.selectedStatements = [];
			$scope.search($scope.filter);
		});
	};

	$scope.calendar = {
		opened: {},
		dateOptions: {
			formatYear: 'yy',
			startingDay: 1
		},
		open: function ($event, which) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.calendar.opened[which] = true;
		}
	};

	$scope.openARCommentsModal = function (patientStatementNote, statement) {
		var modalInstance = $uibModal.open({
			templateUrl: '_statement_ar_comment_form.html',
			controller: 'StatementARCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				patientStatementNote: patientStatementNote,
				statement: statement
			}
		});
		modalInstance.result.then(function () {
			$scope.search($scope.filter);
		});
	};

	$scope.openPatientStatementCommentsModal = function (statement, index) {
		var modalInstance = $uibModal.open({
			templateUrl: 'patient_statement_comments_modal_form.html',
			controller: 'PatientStatementCommentCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				statement: statement,
			}
		});
		modalInstance.result.then(function (nextComment) {
			$scope.statements[index].nextComment = nextComment;
		});
	};

	$scope.toggleSelection = function (statement) {
		var selected = statement.selected;
		if (selected) {
			statement.userId = $scope.user.id;
			$scope.selectedStatements.push(statement);
		} else {
			angular.forEach($scope.selectedStatements, function (s, index) {
				if (s.claimId === statement.claimId) {
					$scope.selectedStatements.splice(index, 1);
					$scope.wrapper.selectedAll = false;
				}
			});
		}
		selected = !selected;
		return selected;
	};

	$scope.togglePaymentPlan = function (claim) {
		ClaimFactory.save(claim).$promise.then(function (response) {
		});
	};

	$scope.selectAll = function (statements) {
		var toRemove = [];
		angular.forEach(statements, function (statement, index) {
			var alreadySelected = statement.selected;
			statement.userId = $scope.user.id;
			statement.selected = statement.claim.paymentPlan ? statement.selected : $scope.wrapper.selectedAll;
			if (statement.selected && !alreadySelected) {
				$scope.selectedStatements.push(statement);
			} else if (!statement.selected) {
				toRemove.push(statement);
			}
		});
		if (toRemove.length > 0) {
			// The array works backwards to prevent only half of the results from being removed
			for (var i = $scope.selectedStatements.length - 1; i >= 0; i--) {
				for (var j = 0; j < toRemove.length; j++) {
					if ($scope.selectedStatements[i] && ($scope.selectedStatements[i].claimId === toRemove[j].claimId)) {
						$scope.selectedStatements.splice(i, 1);
					}
				}
			}
		}
		return statements;
	};

	$scope.loadApplyPostedPayment = function (payment) {
		PaymentService.openApplyPaymentModal(payment, '_main_patient_insurance_modal_form.html', null, function () {
			$scope.search();
		});
	};

	$scope.$watch('filter.claimId', function (newValue, oldValue) {
		if (newValue != null && Number(newValue) > 0) {
			$scope.clearFilters("claimId");
		}
	});

	$scope.$watch('filter.prescriptionId', function (newValue, oldValue) {
		if (newValue != null && Number(newValue) > 0) {
			$scope.clearFilters("prescriptionId");
		}
	});


	$scope.clearFilters = function (except) {
		$scope.filter.attempt = "all";
		$scope.filter.branchId = null;
		if (except !== "claimId") $scope.filter.claimId = null;
		$scope.filter.dosEndDate = "";
		$scope.filter.dosStartDate = "";
		$scope.filter.lastSentEndDate = null;
		$scope.filter.lastSentStartDate = null;
		$scope.filter.unappliedPayments = false;
		if (except !== "patient") {
			$scope.filter.patient = null;
			$scope.filter.patientId = null;
		}
		if (except !== "prescriptionId") $scope.filter.prescriptionId = null;
	};
}
