package com.nymbl.poc;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * NYM-4181: Connection Leak Detection POC
 *
 * Simple standalone class to intentionally leak database connections
 * and observe HikariCP's leak detection logging in action.
 *
 * Run this class directly to simulate connection leaks.
 */
@Slf4j
public class ConnectionLeakSimulator {

    private static final List<Connection> leakedConnections = new ArrayList<>();
    private static HikariDataSource dataSource;

    public static void main(String[] args) {
        log.info("🔥 Starting Connection Leak Simulator POC");

        try {
            setupHikariPool();
            simulateConnectionLeaks();
        } catch (Exception e) {
            log.error("Error in leak simulator", e);
        } finally {
            if (dataSource != null) {
                dataSource.close();
            }
        }
    }

    private static void setupHikariPool() {
        log.info("Setting up HikariCP pool with leak detection...");

        HikariConfig config = new HikariConfig();

        // Use actual Nymbl database connection details
        config.setJdbcUrl("***********************************************************************************");
        config.setUsername("root");
        config.setPassword("P49ikJr8rjH4udoK4rhH4jdpBH");
        config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");

        // Small pool for easier leak detection
        config.setMaximumPoolSize(5);
        config.setMinimumIdle(2);

        // Aggressive leak detection - 10 seconds instead of 60
        config.setLeakDetectionThreshold(10_000);
        config.setConnectionTimeout(30_000);
        config.setIdleTimeout(600_000);
        config.setMaxLifetime(1_800_000);
        config.setConnectionTestQuery("SELECT 1");
        config.setPoolName("leak-simulator-pool");

        dataSource = new HikariDataSource(config);
        log.info("✅ HikariCP pool created with leak detection threshold: 10 seconds");
    }

    private static void simulateConnectionLeaks() throws InterruptedException {
        log.info("🚨 Starting connection leak simulation...");
        log.info("Watch for leak detection logs after 10+ seconds...");

        int leakCount = 0;

        while (true) {
            try {
                // Get a connection and intentionally don't close it
                Connection connection = dataSource.getConnection();
                leakedConnections.add(connection);
                leakCount++;

                log.info("💧 Leaked connection #{} - Total active connections: {}",
                    leakCount, dataSource.getHikariPoolMXBean().getActiveConnections());

                // Wait 15 seconds between leaks to see detection logs
                Thread.sleep(15_000);

                // Stop after 10 leaks to avoid overwhelming the system
                if (leakCount >= 10) {
                    log.info("🛑 Stopping after {} leaks. Pool exhausted.", leakCount);
                    break;
                }

            } catch (SQLException e) {
                log.error("Failed to get connection (pool likely exhausted): {}", e.getMessage());
                break;
            }
        }

        log.info("🔍 Leak simulation complete. Check logs above for HikariCP leak detection messages.");
        log.info("💡 Look for messages containing 'Connection leak detection' or similar.");

        // Keep the app running to see any delayed leak detection logs
        log.info("⏳ Keeping app alive for 2 more minutes to catch any delayed leak detection...");
        Thread.sleep(120_000);
    }
}
