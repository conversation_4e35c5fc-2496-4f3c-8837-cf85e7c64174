package com.nymbl.poc;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class ConnectionLeakSimulator {

    private static final List<Connection> leakedConnections = new ArrayList<>();
    private static HikariDataSource dataSource;

    public static void main(String[] args) throws Exception {
        setupHikariPool();

        while (true) {
            Connection connection = dataSource.getConnection();
            leakedConnections.add(connection);
            System.out.println("Leaked connection #" + leakedConnections.size());
            Thread.sleep(10_000);
        }
    }

    private static void setupHikariPool() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*****************************************************");
        config.setUsername("root");
        config.setPassword("P49ikJr8rjH4udoK4rhH4jdpBH");
        config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");
        config.setMaximumPoolSize(5);
        config.setLeakDetectionThreshold(10_000);
        config.setPoolName("leak-simulator-pool");
        dataSource = new HikariDataSource(config);
    }
}
