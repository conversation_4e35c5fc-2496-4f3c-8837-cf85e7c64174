package com.nymbl.poc;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class ConnectionLeakSimulator {

    private static final Logger logger = LoggerFactory.getLogger(ConnectionLeakSimulator.class);
    private static final List<Connection> leakedConnections = new ArrayList<>();
    private static HikariDataSource dataSource;

    public static void main(String[] args) throws Exception {
        System.out.println("Starting HikariCP Connection Leak Detection POC");
        setupHikariPool();

        while (true) {
            Connection connection = dataSource.getConnection();
            leakedConnections.add(connection);
            System.out.println("Leaked connection #" + leakedConnections.size() + " - waiting 70 seconds...");
            logger.info("Leaked connection #{} - Watch for HikariCP leak detection warnings...", leakedConnections.size());
            Thread.sleep(70_000); // Wait 70 seconds to ensure leak detection triggers
        }
    }

    private static void setupHikariPool() {
        logger.info("Setting up HikariCP pool with leak detection...");

        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*****************************************************");
        config.setUsername("root");
        config.setPassword("P49ikJr8rjH4udoK4rhH4jdpBH");
        config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");
        config.setMaximumPoolSize(5);
        config.setMinimumIdle(2);

        // Use production-like leak detection threshold
        config.setLeakDetectionThreshold(60_000); // 60 seconds like production
        config.setConnectionTimeout(30_000);
        config.setIdleTimeout(600_000);
        config.setMaxLifetime(1_800_000);
        config.setConnectionTestQuery("SELECT 1");
        config.setPoolName("leak-simulator-pool");

        dataSource = new HikariDataSource(config);
        logger.info("HikariCP pool created with 60-second leak detection threshold");
    }
}
