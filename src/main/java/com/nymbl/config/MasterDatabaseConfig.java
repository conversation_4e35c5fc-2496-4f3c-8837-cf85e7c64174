package com.nymbl.config;

import com.nymbl.audit.AuditorAwareImpl;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import jakarta.persistence.EntityManagerFactory;
import org.hibernate.envers.configuration.EnversSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Bradley Moore on 05/16/2017.
 */
@Slf4j
@Configuration
//@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan("com.nymbl.master")
@EnableJpaRepositories(
        entityManagerFactoryRef = "masterEntityManager",
        transactionManagerRef = "masterTransactionManager",
        basePackages = {"com.nymbl.master.repository"}
)
@EnableTransactionManagement(proxyTargetClass = true)
@EnableJpaAuditing(auditorAwareRef = "masterAuditProvider")
public class MasterDatabaseConfig {

    private final Environment environment;

    @Value("${db.url}")
    String url;

    @Value("${db.username}")
    String username;

    @Value("${db.password}")
    String password;

    // Parameter Store property to enable HikariCP
    @Value("${nymbl.database.hikari.enabled:false}")
    boolean hikariEnabled;

    @Autowired
    public MasterDatabaseConfig(Environment environment) {
        this.environment = environment;
    }

    @Primary
    @Bean(destroyMethod = "close")
    public DataSource dataSource() {
        if (hikariEnabled) {
            log.info("🔥 MASTER DATABASE: Using HikariCP Connection Pool (hikari.enabled=true)");
            return createHikariDataSource();
        } else {
            log.info("🐱 MASTER DATABASE: Using Tomcat JDBC Connection Pool (hikari.enabled=false)");
            return createTomcatDataSource();
        }
    }

    private DataSource createHikariDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");

        // Pool sizing based on environment
        if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
            config.setMaximumPoolSize(20);
            config.setMinimumIdle(5);
        } else {
            config.setMaximumPoolSize(1_000);
            config.setMinimumIdle(50);
        }

        // Connection settings
        config.setConnectionTimeout(30_000);
        config.setIdleTimeout(600_000);
        config.setMaxLifetime(1_800_000);
        config.setLeakDetectionThreshold(60_000);
        config.setConnectionTestQuery("SELECT 1");
        config.setPoolName("master-hikari-pool");

        return new HikariDataSource(config);
    }

    private DataSource createTomcatDataSource() {
        // Original Tomcat JDBC implementation
        org.apache.tomcat.jdbc.pool.DataSource ds = new org.apache.tomcat.jdbc.pool.DataSource();
        ds.setUrl(url);
        ds.setUsername(username);
        ds.setPassword(password);
        ds.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");

        if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
            ds.setMaxActive(20);
            ds.setInitialSize(10);
            ds.setMaxIdle(10);
            ds.setMinIdle(5);
        } else {
            ds.setMaxActive(1_000);
            ds.setInitialSize(100);
            ds.setMaxIdle(100);
            ds.setMinIdle(50);
        }

        ds.setRemoveAbandoned(true);
        ds.setRemoveAbandonedTimeout(600);
        ds.setTimeBetweenEvictionRunsMillis(30_000);
        ds.setMinEvictableIdleTimeMillis(60_000);
        ds.setLogValidationErrors(true);
        ds.setTestOnConnect(true);
        ds.setTestWhileIdle(true);
        ds.setTestOnBorrow(true);
        ds.setValidationQuery("SELECT 1");

        return ds;
    }

    @Primary
    @Bean(name = "masterEntityManager")
    public LocalContainerEntityManagerFactoryBean masterEntityManager() {
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabase(Database.valueOf("MYSQL"));

        LocalContainerEntityManagerFactoryBean factoryBean = new LocalContainerEntityManagerFactoryBean();
        factoryBean.setDataSource(dataSource());
        factoryBean.setJpaVendorAdapter(vendorAdapter);
        factoryBean.setPackagesToScan("com.nymbl.master.model");
        factoryBean.setPersistenceUnitName("master");

        Map<String, Object> properties = new HashMap<>();
//        properties.put(org.hibernate.cfg.Environment.PHYSICAL_NAMING_STRATEGY, "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
        properties.put(org.hibernate.cfg.Environment.PHYSICAL_NAMING_STRATEGY, "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
        properties.put(org.hibernate.cfg.Environment.FORMAT_SQL, "true");
        //Solid gold!
        //properties.put(org.hibernate.cfg.Environment.SHOW_SQL, "true");
        properties.put(EnversSettings.AUDIT_TABLE_SUFFIX, "_audit");
        properties.put(EnversSettings.REVISION_FIELD_NAME, "revision_id");
        properties.put(EnversSettings.REVISION_TYPE_FIELD_NAME, "revision_type");
        properties.put(EnversSettings.STORE_DATA_AT_DELETE, "true");// populate the type 2 'delete' record with the final state data

        properties.put(org.hibernate.cfg.Environment.ENABLE_LAZY_LOAD_NO_TRANS, "true");
        factoryBean.setJpaPropertyMap(properties);

        return factoryBean;
    }

    @Primary
    @Bean(name = "masterTransactionManager")
    public PlatformTransactionManager masterTransactionManager(@Qualifier("masterEntityManager") EntityManagerFactory masterEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(masterEntityManager);
        return transactionManager;
    }

    @Bean(name = "masterAuditProvider")
    public AuditorAware<String> masterAuditProvider() {
        return new AuditorAwareImpl();
    }

}
